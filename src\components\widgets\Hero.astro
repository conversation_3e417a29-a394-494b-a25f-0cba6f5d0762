---
import Image from '~/components/common/Image.astro';
import Button from '~/components/ui/Button.astro';

import type { Hero as Props } from '~/types';

const {
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  tagline,

  content = await Astro.slots.render('content'),
  actions = await Astro.slots.render('actions'),
  image = await Astro.slots.render('image'),

  id,
  bg = await Astro.slots.render('bg'),
} = Astro.props;
---

<section class="relative h-screen w-full not-prose" {...id ? { id } : {}}>
  <!-- 背景图片层 -->
  {image && (
    <div class="absolute inset-0 z-0">
      {typeof image === 'string' ? (
        <Fragment set:html={image} />
      ) : (
        <Image
          class="w-full h-full object-cover"
          widths={[400, 768, 1024, 2040]}
          sizes="(max-width: 767px) 400px, (max-width: 1023px) 768px, (max-width: 2039px) 1024px, 2040px"
          loading="eager"
          width={2040}
          height={1080}
          {...image}
        />
      )}
      <!-- 轻微遮罩层 -->
      <div class="absolute inset-0 bg-black/20"></div>
    </div>
  )}

  <!-- 粒子背景层 -->
  <div class="absolute inset-0 pointer-events-none z-10" aria-hidden="true">
    <slot name="bg">
      {bg ? <Fragment set:html={bg} /> : null}
    </slot>
  </div>
  <div class="relative h-full flex items-center justify-center px-4 sm:px-6 z-20">
    <div class="text-center max-w-5xl mx-auto">
        {
          tagline && (
            <p
              class="text-base text-white/80 font-bold tracking-wide uppercase intersect-once intersect-quarter motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade"
              set:html={tagline}
            />
          )
        }
        {
          title && (
            <h1
              class="text-5xl md:text-6xl font-bold leading-tighter tracking-tighter mb-4 font-heading text-white intersect-once intersect-quarter motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade"
              set:html={title}
            />
          )
        }
        <div class="max-w-3xl mx-auto">
          {
            subtitle && (
              <p
                class="text-xl text-white/90 mb-6 intersect-once intersect-quarter motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade"
                set:html={subtitle}
              />
            )
          }
          {
            actions && (
              <div class="max-w-xs sm:max-w-md m-auto flex flex-nowrap flex-col sm:flex-row sm:justify-center gap-4 intersect-once intersect-quarter motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade">
                {Array.isArray(actions) ? (
                  actions.map((action) => (
                    <div class="flex w-full sm:w-auto">
                      <Button {...(action || {})} class="w-full sm:mb-0" />
                    </div>
                  ))
                ) : (
                  <Fragment set:html={actions} />
                )}
              </div>
            )
          }
        </div>
        {content && <Fragment set:html={content} />}

        <!-- 支持我们卡片 -->
        <div class="mt-8 md:mt-12 px-4 sm:px-6 intersect-once intersect-quarter motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade">
          <div class="max-w-xs sm:max-w-sm md:max-w-md mx-auto bg-white/10 backdrop-blur-md rounded-2xl p-4 md:p-6 border border-white/20 shadow-xl hover:bg-white/15 hover:shadow-2xl transition-all duration-300 hover:scale-[1.02]">
            <div class="text-center">
              <div class="mb-3 md:mb-4">
                <div class="inline-flex items-center justify-center w-10 h-10 md:w-12 md:h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full mb-2 md:mb-3 shadow-lg animate-pulse">
                  <svg class="w-5 h-5 md:w-6 md:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <h3 class="text-lg md:text-xl font-bold text-white mb-1 md:mb-2 tracking-wide">支持我们</h3>
                <p class="text-white/80 text-xs md:text-sm mb-3 md:mb-4 leading-relaxed px-2">
                  帮助我们继续开发、维护和推广我们的项目
                </p>
              </div>
              <div class="flex flex-col gap-2.5 md:gap-3 justify-center">
                <a
                  href="/donate"
                  class="group inline-flex items-center justify-center px-5 py-2.5 md:py-2.5 bg-gradient-to-r from-red-500 to-pink-500 text-white font-medium text-sm rounded-full hover:from-red-600 hover:to-pink-600 transition-all duration-200 transform hover:scale-105 shadow-lg active:scale-95 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-transparent"
                >
                  <svg class="w-4 h-4 mr-2 group-hover:animate-bounce" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                  </svg>
                  捐赠我们
                </a>
                <a
                  href="/ycsj"
                  class="group inline-flex items-center justify-center px-5 py-2.5 md:py-2.5 bg-white/20 text-white font-medium text-sm rounded-full hover:bg-white/30 transition-all duration-200 border border-white/30 active:scale-95 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-transparent"
                >
                  <svg class="w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                  </svg>
                  远程刷机
                </a>
              </div>
            </div>
          </div>
        </div>
    </div>
  </div>
</section>
